╔══════════════════════════════════════════════════════════════╗
║                    Python安装指导                           ║
╚══════════════════════════════════════════════════════════════╝

如果双击"启动程序.bat"提示缺少Python，请按以下步骤安装：

📋 安装步骤：
1. 访问Python官网：https://www.python.org/downloads/
2. 点击"Download Python"下载最新版本
3. 运行下载的安装程序
4. ⚠️ 重要：勾选"Add Python to PATH"选项
5. 点击"Install Now"开始安装
6. 安装完成后重启电脑
7. 重新运行"启动程序.bat"

💡 安装提示：
- 选择"Add Python to PATH"非常重要
- 如果忘记勾选，需要重新安装Python
- 安装过程需要管理员权限

🔧 验证安装：
安装完成后，可以按Win+R，输入cmd，然后输入：
python --version
如果显示版本号，说明安装成功

❓ 常见问题：
Q: 提示"python不是内部或外部命令"？
A: 说明没有勾选"Add Python to PATH"，请重新安装

Q: 安装失败？
A: 确保以管理员身份运行安装程序
