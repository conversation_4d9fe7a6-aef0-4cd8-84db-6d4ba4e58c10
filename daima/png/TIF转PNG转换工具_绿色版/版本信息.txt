╔══════════════════════════════════════════════════════════════╗
║                    TIF转PNG转换工具                          ║
║                      版本信息                                ║
╚══════════════════════════════════════════════════════════════╝

📋 软件信息：
软件名称：TIF转PNG转换工具
版本号：v1.0.0
发布日期：2024年7月4日
软件类型：绿色免安装版

🎯 功能特点：
✅ 支持单文件转换和批量转换
✅ 支持.tif和.tiff格式文件
✅ 智能颜色模式处理（RGB、RGBA、灰度、调色板等）
✅ 自动保持图片质量和透明度
✅ 友好的图形用户界面
✅ 详细的转换进度显示
✅ 内置测试图片验证功能
✅ 绿色软件，无需安装

💻 系统要求：
操作系统：Windows 7/8/10/11
Python版本：3.7或更高版本
必需库：Pillow (PIL)
可选库：tkinter (通常随Python安装)

📦 包含文件：
- 启动程序.bat (主启动器)
- 简单启动器.bat (备用启动器)
- tif_to_png_gui.py (图形界面主程序)
- tif.py (转换功能模块)
- create_test_images.py (测试图片生成器)
- test_images/ (测试图片文件夹，包含7张测试图片)
- README.txt (快速使用说明)
- Python安装指导.txt (Python安装步骤)
- 用户指南.txt (详细使用指南)
- 故障排除指南.txt (问题解决方案)
- 版本信息.txt (本文件)

🔄 更新历史：
v1.0.0 (2024-07-04)
- 首次发布
- 实现基本的TIF转PNG转换功能
- 支持单文件和批量转换
- 添加图形用户界面
- 包含完整的用户文档
- 提供测试图片和故障排除指南

🛠️ 技术细节：
编程语言：Python 3.x
GUI框架：tkinter
图像处理：Pillow (PIL)
打包方式：绿色便携版
文件编码：UTF-8

📞 技术支持：
如遇到问题，请参考：
1. 故障排除指南.txt
2. 用户指南.txt
3. Python安装指导.txt

🎉 感谢使用TIF转PNG转换工具！
