╔══════════════════════════════════════════════════════════════╗
║                    故障排除指南                              ║
╚══════════════════════════════════════════════════════════════╝

🔧 常见问题及解决方案：

【问题1】双击启动器没反应或出现乱码
💡 解决方案：
- 尝试双击"简单启动器.bat"
- 右键选择"以管理员身份运行"
- 检查是否被杀毒软件拦截
- 确保文件路径不包含中文或特殊字符

【问题2】提示"找不到Python"或"python不是内部命令"
💡 解决方案：
- 按照"Python安装指导.txt"安装Python
- 确保安装时勾选了"Add Python to PATH"
- 安装后重启电脑
- 验证安装：按Win+R，输入cmd，然后输入 python --version

【问题3】提示"PIL库未安装"或图像处理错误
💡 解决方案：
- 程序会自动尝试安装
- 如果自动安装失败，手动运行：pip install Pillow
- 确保网络连接正常

【问题4】程序启动后界面不显示
💡 解决方案：
- 检查任务栏是否有程序图标
- 按Alt+Tab切换窗口
- 重启程序
- 检查屏幕分辨率设置

【问题5】转换失败或提示文件错误
💡 解决方案：
- 确保输入文件是有效的TIF/TIFF格式
- 检查文件是否损坏（用其他软件打开测试）
- 确保磁盘空间充足
- 确保对文件夹有读写权限

【问题6】程序运行缓慢
💡 解决方案：
- 大文件转换需要时间，请耐心等待
- 关闭其他占用内存的程序
- 一次处理的文件数量不要太多
- 考虑分批处理大量文件

【问题7】转换后图片质量问题
💡 解决方案：
- 程序使用最佳质量设置
- 如果原图质量就不高，转换后也会如此
- PNG格式可能比TIF文件大，这是正常的
- 检查原始TIF文件是否完整

【问题8】路径或文件名包含中文字符
💡 解决方案：
- 将程序移动到英文路径下
- 避免使用包含中文的文件夹名
- 重命名包含中文的TIF文件

【问题9】权限相关错误
💡 解决方案：
- 右键选择"以管理员身份运行"
- 确保对目标文件夹有写入权限
- 检查文件是否被其他程序占用

【问题10】网络相关错误（安装依赖时）
💡 解决方案：
- 确保网络连接正常
- 尝试使用手机热点
- 配置代理设置（如果在公司网络）
- 手动下载Pillow库安装包

🆘 紧急解决方案：
如果所有启动器都无法使用：
1. 按Win+R，输入cmd
2. 使用cd命令进入程序目录
3. 直接运行：python tif_to_png_gui.py

📞 获取帮助：
如果以上方案都无法解决问题，请：
1. 截图错误信息
2. 记录详细的操作步骤
3. 注明操作系统版本
4. 联系技术支持

💡 预防措施：
- 定期更新Python到最新版本
- 保持系统更新
- 使用英文路径
- 定期清理临时文件
