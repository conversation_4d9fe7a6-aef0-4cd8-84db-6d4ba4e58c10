@echo off
title TIF to PNG Converter - Smart Launcher

echo.
echo ================================================
echo    TIF to PNG Converter Tool - Smart Launcher
echo ================================================
echo.

cd /d "%~dp0"

echo [1/4] Checking Python environment...
echo.

REM Check for portable Python first
if exist "python_portable\python.exe" (
    echo [OK] Found portable Python environment
    set PYTHON_CMD=python_portable\python.exe
    goto :check_libs
)

REM Check system Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found on this system
    echo.
    echo SOLUTION: Install Python
    echo 1. Visit: https://www.python.org/downloads/
    echo 2. Download Python 3.7 or higher
    echo 3. IMPORTANT: Check "Add Python to PATH" during installation
    echo 4. Restart computer after installation
    echo 5. Run this program again
    echo.
    echo For detailed instructions, see "Python Installation Guide.txt"
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
) else (
    echo [OK] System Python found:
    python --version
    set PYTHON_CMD=python
)

:check_libs
echo.
echo [2/4] Checking required libraries...

REM Check PIL library
%PYTHON_CMD% -c "import PIL; print('[OK] Image processing library available')" 2>nul
if errorlevel 1 (
    echo [INFO] Installing image processing library (Pillow)...
    echo This may take a moment...
    %PYTHON_CMD% -m pip install Pillow
    if errorlevel 1 (
        echo [ERROR] Failed to install image processing library
        echo.
        echo SOLUTION: Manual installation
        echo 1. Open command prompt as administrator
        echo 2. Run: pip install Pillow
        echo 3. Try running this program again
        echo.
        pause
        exit /b 1
    )
    echo [OK] Image processing library installed successfully
)

REM Check GUI library
%PYTHON_CMD% -c "import tkinter; print('[OK] GUI library available')" 2>nul
if errorlevel 1 (
    echo [ERROR] GUI library (tkinter) not available
    echo.
    echo SOLUTION: Reinstall Python
    echo Make sure to install the complete Python package including tkinter
    echo.
    pause
    exit /b 1
)

echo.
echo [3/4] Checking program files...

REM Check main program
if not exist "tif_to_png_gui.py" (
    echo [ERROR] Main program file not found: tif_to_png_gui.py
    echo Make sure all files are in the same folder
    pause
    exit /b 1
)

if not exist "tif.py" (
    echo [ERROR] Converter module not found: tif.py
    echo Make sure all files are in the same folder
    pause
    exit /b 1
)

echo [OK] All program files found

REM Create test images if needed
if not exist "test_images" (
    if exist "create_test_images.py" (
        echo [INFO] Creating test images for first-time use...
        %PYTHON_CMD% create_test_images.py
        echo [OK] Test images created
    )
)

echo.
echo [4/4] Starting TIF to PNG Converter...
echo.
echo ================================================
echo USAGE TIPS:
echo - Choose "Single File" to convert one TIF file
echo - Choose "Batch Convert" to convert a folder of TIF files
echo - PNG files are saved to the original location by default
echo - Use the "test_images" folder to test the program
echo ================================================
echo.

REM Launch the GUI program
%PYTHON_CMD% tif_to_png_gui.py

REM Handle program exit
echo.
if errorlevel 1 (
    echo [ERROR] The program encountered an error
    echo.
    echo COMMON CAUSES:
    echo 1. File permission issues
    echo 2. Insufficient disk space
    echo 3. Corrupted image files
    echo 4. Special characters in file paths
    echo.
    echo SOLUTIONS:
    echo 1. Run this program as administrator
    echo 2. Check available disk space
    echo 3. Move program to a folder with English name only
    echo 4. Try converting different image files
    echo.
    echo For more help, see "Troubleshooting Guide.txt"
) else (
    echo [OK] Program completed successfully
)

echo.
echo Thank you for using TIF to PNG Converter!
echo Press any key to close this window...
pause >nul
