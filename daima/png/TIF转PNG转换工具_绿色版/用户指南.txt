===============================================
    TIF转PNG转换工具 - 普通用户使用指南
===============================================

🎯 这是一个简单易用的图片格式转换工具，可以将TIF格式的图片转换为PNG格式。

===============================================
📦 获取可执行版本（推荐）
===============================================

【方法一：使用现成的exe文件】
如果开发者已经提供了打包好的exe文件：
1. 下载"发布版本"文件夹
2. 双击"TIF转PNG转换工具.exe"即可使用
3. 无需安装任何软件，直接运行

【方法二：自己打包exe文件】
如果你有Python环境，可以自己打包：
1. 双击"一键打包.bat"文件
2. 等待自动安装依赖和打包完成
3. 在"发布版本"文件夹中找到exe文件

===============================================
🖥️ 使用exe版本（最简单）
===============================================

1. 【启动程序】
   双击"TIF转PNG转换工具.exe"

2. 【选择模式】
   ○ 单文件转换：转换一个TIF文件
   ○ 批量转换：转换整个文件夹的TIF文件

3. 【选择文件】
   点击"选择文件"或"选择目录"按钮

4. 【开始转换】
   点击"🚀 开始转换"按钮

5. 【查看结果】
   转换完成后，PNG文件会保存在原文件目录

===============================================
💻 如果没有exe文件（需要Python）
===============================================

【前提条件】
需要在电脑上安装：
- Python 3.7或更高版本
- PIL图像处理库

【安装步骤】
1. 下载并安装Python：https://www.python.org/
2. 打开命令提示符，输入：pip install Pillow
3. 双击"启动GUI.bat"文件启动程序

===============================================
📋 界面说明
===============================================

程序界面包含以下部分：

┌─────────────────────────────────────────┐
│        TIF to PNG 转换工具              │  ← 标题
├─────────────────────────────────────────┤
│ ○单文件转换 ●批量转换                   │  ← 选择模式
├─────────────────────────────────────────┤
│ 输入: [文件路径...] [选择文件]          │  ← 选择要转换的文件
│ 输出: [保存路径...] [选择目录]          │  ← 选择保存位置（可选）
├─────────────────────────────────────────┤
│    [🚀 开始转换] [🗑️ 清空]             │  ← 操作按钮
├─────────────────────────────────────────┤
│ 进度: 准备就绪                          │  ← 进度显示
│ ████████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ │  ← 进度条
├─────────────────────────────────────────┤
│ 转换日志:                               │  ← 详细日志
│ ✅ 程序启动成功                         │
│ 🔄 开始转换...                          │
│ ✅ 转换完成                             │
└─────────────────────────────────────────┘

===============================================
🎯 使用示例
===============================================

【示例1：转换单个照片】
1. 选择"单文件转换"
2. 点击"选择文件"，找到你的照片.tif
3. 点击"开始转换"
4. 完成后得到照片.png

【示例2：批量转换文件夹】
1. 选择"批量转换"
2. 点击"选择目录"，选择包含多个TIF文件的文件夹
3. 点击"开始转换"
4. 所有TIF文件都会转换为PNG格式

===============================================
💡 使用技巧
===============================================

✅ 支持的文件格式：.tif, .tiff
✅ 输出格式：.png
✅ 如果不选择输出位置，PNG文件会保存在原TIF文件的同一目录
✅ 批量转换时，程序会自动跳过非TIF文件
✅ 转换过程中可以在日志区域查看详细进度
✅ 大文件转换可能需要较长时间，请耐心等待

===============================================
❓ 常见问题
===============================================

【问题】双击exe文件没反应？
【解决】检查是否被杀毒软件拦截，尝试添加信任

【问题】提示"找不到文件"？
【解决】确保选择的是.tif或.tiff格式的文件

【问题】转换很慢？
【解决】大文件转换需要时间，请耐心等待

【问题】转换失败？
【解决】检查文件是否损坏，是否有足够的磁盘空间

【问题】想要命令行版本？
【解决】在命令提示符中使用：python tif.py 文件名.tif

===============================================
📞 技术支持
===============================================

如果遇到问题：
1. 查看"使用方法.txt"文件获取详细说明
2. 检查文件格式是否正确（必须是.tif或.tiff）
3. 确保有足够的磁盘空间
4. 尝试重新启动程序

===============================================
🎉 开始使用吧！
===============================================

这个工具设计得非常简单，即使不懂编程也能轻松使用。
只需要几次点击就能完成图片格式转换！
