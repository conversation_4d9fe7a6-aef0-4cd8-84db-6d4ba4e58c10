╔══════════════════════════════════════════════════════════════╗
║                    快速开始指南                              ║
╚══════════════════════════════════════════════════════════════╝

🚀 3步开始使用：

【步骤1】准备环境（仅首次需要）
如果电脑没有Python：
1. 访问 https://www.python.org/downloads/
2. 下载并安装Python（记得勾选"Add Python to PATH"）
3. 重启电脑

【步骤2】启动程序
双击 "启动程序.bat" 文件
（如果不行，试试双击 "简单启动器.bat"）

【步骤3】开始转换
1. 选择转换模式（单文件或批量）
2. 选择TIF文件或文件夹
3. 点击"开始转换"

🧪 测试功能：
想先测试一下？
1. 启动程序后
2. 选择"批量转换"
3. 选择"test_images"文件夹
4. 点击"开始转换"
5. 查看转换结果

💡 常用操作：

【转换单个文件】
1. 选择"单文件转换"
2. 点击"选择文件" → 找到你的.tif文件
3. （可选）点击"选择文件"指定PNG保存位置
4. 点击"开始转换"

【批量转换文件夹】
1. 选择"批量转换"
2. 点击"选择目录" → 选择包含TIF文件的文件夹
3. （可选）点击"选择目录"指定PNG保存文件夹
4. 点击"开始转换"

🎯 就是这么简单！

❓ 遇到问题？
- 查看"故障排除指南.txt"
- 查看"Python安装指导.txt"
- 查看"用户指南.txt"获取详细说明
