# TIF转PNG转换工具 - 完整用户指南

## 🎯 工具简介

这是一个简单易用的图片格式转换工具，专门用于将TIF/TIFF格式的图片转换为PNG格式。支持单文件转换和批量转换，具有友好的图形界面。

---

## 📦 获取和使用方式

### 方式一：便携版（推荐给普通用户）

**适用人群**：不想安装复杂软件的普通用户

**使用步骤**：
1. **获取工具**：下载整个文件夹到电脑
2. **安装Python**（一次性）：
   - 访问 https://www.python.org/downloads/
   - 下载Python 3.7或更高版本
   - 安装时**务必勾选**"Add Python to PATH"
3. **启动工具**：双击 `便携版启动器.bat`
4. **首次运行**：程序会自动安装必要组件
5. **开始使用**：按界面提示操作即可

**优点**：
- ✅ 一次配置，永久使用
- ✅ 自动检查和安装依赖
- ✅ 详细的错误提示和解决方案
- ✅ 包含测试图片

### 方式二：exe可执行文件（最简单）

**适用人群**：完全不想接触技术的用户

**使用步骤**：
1. **获取exe文件**：从开发者处获取打包好的exe文件
2. **直接运行**：双击exe文件即可使用
3. **无需安装**：绿色软件，可复制到任何电脑使用

**优点**：
- ✅ 无需安装任何软件
- ✅ 双击即用
- ✅ 可在任何Windows电脑运行

**获取方式**：
- 如果开发者提供了exe文件，直接使用
- 如果没有，可以使用"一键打包PyInstaller.bat"自己制作

### 方式三：开发者模式

**适用人群**：有Python基础的用户

**使用步骤**：
1. 确保安装了Python和PIL库：`pip install Pillow`
2. 直接运行：`python tif_to_png_gui.py`

---

## 🖥️ 界面使用说明

### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    TIF to PNG 转换工具                      │
├─────────────────────────────────────────────────────────────┤
│ 转换模式: ○ 单文件转换  ● 批量转换                          │
├─────────────────────────────────────────────────────────────┤
│ 输入路径: [C:\图片\我的TIF文件夹]     [选择目录]            │
│ 输出路径: [C:\图片\转换结果]          [选择目录]            │
│ 💡 提示：输出路径为空时，将在原文件目录生成PNG文件          │
├─────────────────────────────────────────────────────────────┤
│           [🚀 开始转换]    [🗑️ 清空]                       │
├─────────────────────────────────────────────────────────────┤
│ 状态: 转换中... (3/10)                                      │
│ ████████████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ │
├─────────────────────────────────────────────────────────────┤
│ 转换日志:                                                   │
│ ✅ 找到 10 个TIF文件，开始转换...                           │
│ [1/10] 🔄 处理: photo1.tif                                 │
│     ✅ 转换成功                                             │
│ [2/10] 🔄 处理: photo2.tif                                 │
│     ✅ 转换成功                                             │
│ ...                                                         │
└─────────────────────────────────────────────────────────────┘
```

### 操作步骤

1. **选择转换模式**
   - **单文件转换**：转换一个TIF文件
   - **批量转换**：转换文件夹中所有TIF文件

2. **选择输入**
   - 单文件模式：点击"选择文件"，选择.tif或.tiff文件
   - 批量模式：点击"选择目录"，选择包含TIF文件的文件夹

3. **选择输出**（可选）
   - 可以指定输出位置
   - 留空则在原文件目录生成PNG文件

4. **开始转换**
   - 点击"🚀 开始转换"按钮
   - 在日志区域查看进度
   - 等待转换完成

---

## 🧪 测试功能

工具包含了7种不同类型的测试图片：

1. **test_rgb.tif** - 标准彩色图片
2. **test_rgba.tif** - 透明图片
3. **test_grayscale.tif** - 灰度图片
4. **test_palette.tif** - 调色板图片
5. **test_gradient.tif** - 渐变图片
6. **test_blackwhite.tif** - 黑白图片
7. **test_large.tif** - 大尺寸图片（性能测试）

**测试步骤**：
1. 启动程序后，测试图片会自动创建在`test_images`文件夹
2. 选择"批量转换"模式
3. 选择`test_images`文件夹
4. 点击"开始转换"
5. 检查转换结果

---

## 💡 使用技巧

### 文件格式支持
- **输入格式**：.tif, .tiff
- **输出格式**：.png
- **颜色模式**：RGB, RGBA, 灰度, 调色板等

### 转换特点
- ✅ 自动保持图片质量
- ✅ 保留透明度信息
- ✅ 智能处理不同颜色模式
- ✅ 批量处理大量文件
- ✅ 详细的进度显示

### 输出设置
- **默认行为**：PNG文件保存在TIF文件同目录
- **自定义输出**：可指定专门的输出文件夹
- **文件命名**：保持原文件名，只改变扩展名

---

## ❓ 常见问题解答

### 安装和启动问题

**Q: 双击启动器没反应？**
A: 检查是否安装了Python，确保安装时勾选了"Add Python to PATH"

**Q: 提示"找不到Python"？**
A: 重新安装Python，或者手动添加Python到系统PATH

**Q: 提示"PIL库未安装"？**
A: 运行命令：`pip install Pillow`

### 使用问题

**Q: 找不到TIF文件？**
A: 确保文件扩展名是.tif或.tiff，检查文件是否损坏

**Q: 转换很慢？**
A: 大文件需要时间，请耐心等待。可以先用小文件测试

**Q: 转换失败？**
A: 检查磁盘空间、文件权限、文件是否损坏

**Q: 想要其他格式？**
A: 目前只支持TIF转PNG，如需其他格式请联系开发者

### 高级问题

**Q: 如何批量处理子文件夹？**
A: 目前只处理选定文件夹，不包含子文件夹

**Q: 如何保持原始文件结构？**
A: 选择输出目录时，程序会保持文件名，但不保持文件夹结构

**Q: 如何自定义转换参数？**
A: 目前使用默认最佳参数，如需自定义请使用命令行版本

---

## 🔧 故障排除

### 环境问题
1. **Python版本**：确保使用Python 3.7+
2. **依赖库**：确保安装了Pillow库
3. **系统权限**：某些情况下需要管理员权限

### 文件问题
1. **文件格式**：确保是有效的TIF/TIFF文件
2. **文件路径**：避免包含特殊字符的路径
3. **磁盘空间**：确保有足够空间存储转换结果

### 性能问题
1. **大文件**：大文件转换需要更多时间和内存
2. **批量处理**：一次处理太多文件可能较慢
3. **系统资源**：关闭不必要的程序释放内存

---

## 📞 技术支持

如果遇到问题：

1. **查看日志**：程序界面的日志区域有详细信息
2. **检查文件**：确保TIF文件完整且格式正确
3. **重启程序**：有时重启可以解决临时问题
4. **更新Python**：使用最新版本的Python和Pillow库

---

## 🎉 开始使用

选择适合你的方式开始使用：

- **普通用户**：使用便携版启动器
- **追求简单**：使用exe可执行文件
- **开发者**：直接运行Python脚本

无论选择哪种方式，这个工具都能帮你轻松完成TIF到PNG的转换！
