@echo off
chcp 65001 >nul
title TIF转PNG转换工具 - 便携版

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG转换工具                          ║
echo ║                      便携版启动器                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 正在检查运行环境...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python（推荐）：
    echo    - 访问 https://www.python.org/downloads/
    echo    - 下载并安装Python 3.7或更高版本
    echo    - 安装时勾选"Add Python to PATH"
    echo.
    echo 2. 使用便携版Python（高级用户）：
    echo    - 下载便携版Python解压到当前目录
    echo    - 重命名为"python_portable"文件夹
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo ✅ Python环境检测成功
python --version

REM 检查PIL库
echo 🔍 检查图像处理库...
python -c "import PIL; print('✅ PIL库版本:', PIL.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PIL库未安装
    echo 💡 正在自动安装PIL库...
    echo.
    pip install Pillow
    if errorlevel 1 (
        echo ❌ 自动安装失败
        echo.
        echo 💡 请手动安装：
        echo    pip install Pillow
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
    echo ✅ PIL库安装成功
)

REM 检查tkinter
echo 🔍 检查GUI库...
python -c "import tkinter; print('✅ GUI库可用')" 2>nul
if errorlevel 1 (
    echo ❌ GUI库不可用
    echo 💡 请重新安装Python，确保包含tkinter模块
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo.
echo ✅ 所有依赖检查完成
echo.

REM 检查核心文件
if not exist "tif_to_png_gui.py" (
    echo ❌ 找不到主程序文件: tif_to_png_gui.py
    echo 💡 请确保所有程序文件都在当前目录
    pause
    exit /b 1
)

if not exist "tif.py" (
    echo ❌ 找不到转换模块: tif.py
    echo 💡 请确保所有程序文件都在当前目录
    pause
    exit /b 1
)

REM 创建测试图片（如果不存在）
if not exist "test_images" (
    if exist "create_test_images.py" (
        echo 🎨 首次运行，正在创建测试图片...
        python create_test_images.py
        echo.
    )
)

echo 🚀 启动TIF转PNG转换工具...
echo.
echo ═══════════════════════════════════════════════════════════════
echo 💡 使用提示：
echo - 选择"单文件转换"可转换单个TIF文件
echo - 选择"批量转换"可转换整个文件夹的TIF文件
echo - 输出路径可以留空，PNG文件会保存在原目录
echo - 可以使用test_images文件夹中的图片进行测试
echo ═══════════════════════════════════════════════════════════════
echo.

REM 启动GUI程序
python tif_to_png_gui.py

REM 程序退出后的处理
echo.
echo 📋 程序已退出
echo.

REM 检查是否有错误
if errorlevel 1 (
    echo ❌ 程序运行时发生错误
    echo.
    echo 💡 可能的原因：
    echo 1. 文件权限不足
    echo 2. 磁盘空间不足
    echo 3. 文件路径包含特殊字符
    echo 4. 图片文件损坏
    echo.
    echo 🔧 建议解决方案：
    echo 1. 以管理员身份运行
    echo 2. 检查磁盘空间
    echo 3. 将程序移到英文路径下
    echo 4. 尝试转换其他图片文件
    echo.
) else (
    echo ✅ 程序正常退出
)

echo 按任意键关闭窗口...
pause >nul
