@echo off
chcp 65001 >nul
title TIF转PNG工具 - 一键打包

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG转换工具                          ║
echo ║                      一键打包脚本                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔧 开始打包程序...
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤1: 检查Python环境
echo ═══════════════════════════════════════════════════════════════
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请先安装Python 3.7或更高版本
    pause
    exit /b 1
)
echo ✅ Python环境正常
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤2: 检查必要的库
echo ═══════════════════════════════════════════════════════════════

echo 🔍 检查PIL库...
python -c "import PIL; print('✅ PIL版本:', PIL.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PIL库未安装，正在安装...
    pip install Pillow
    if errorlevel 1 (
        echo ❌ PIL库安装失败
        pause
        exit /b 1
    )
)

echo 🔍 检查cx_Freeze库...
python -c "import cx_Freeze; print('✅ cx_Freeze版本:', cx_Freeze.version)" 2>nul
if errorlevel 1 (
    echo ❌ cx_Freeze库未安装，正在安装...
    pip install cx_Freeze
    if errorlevel 1 (
        echo ❌ cx_Freeze库安装失败
        pause
        exit /b 1
    )
)

echo ✅ 所有依赖库检查完成
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤3: 清理旧的构建文件
echo ═══════════════════════════════════════════════════════════════
if exist "build" (
    echo 🗑️ 删除旧的build目录...
    rmdir /s /q "build"
)
if exist "dist" (
    echo 🗑️ 删除旧的dist目录...
    rmdir /s /q "dist"
)
echo ✅ 清理完成
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤4: 创建测试图片（如果不存在）
echo ═══════════════════════════════════════════════════════════════
if not exist "test_images" (
    echo 🎨 创建测试图片...
    python create_test_images.py
    if errorlevel 1 (
        echo ⚠️ 测试图片创建失败，但不影响打包
    )
) else (
    echo ✅ 测试图片已存在
)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤5: 开始打包
echo ═══════════════════════════════════════════════════════════════
echo 🚀 正在打包，请稍候...
echo.

python setup_exe.py build

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 💡 可能的原因：
    echo    1. 缺少必要的依赖库
    echo    2. Python版本不兼容
    echo    3. 磁盘空间不足
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 打包成功！
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤6: 整理输出文件
echo ═══════════════════════════════════════════════════════════════

if exist "build\exe.win-amd64-3.8" (
    set BUILD_DIR=build\exe.win-amd64-3.8
) else if exist "build\exe.win-amd64-3.9" (
    set BUILD_DIR=build\exe.win-amd64-3.9
) else if exist "build\exe.win-amd64-3.10" (
    set BUILD_DIR=build\exe.win-amd64-3.10
) else if exist "build\exe.win-amd64-3.11" (
    set BUILD_DIR=build\exe.win-amd64-3.11
) else if exist "build\exe.win-amd64-3.12" (
    set BUILD_DIR=build\exe.win-amd64-3.12
) else (
    for /d %%i in (build\exe.*) do set BUILD_DIR=%%i
)

if exist "%BUILD_DIR%" (
    echo 📦 创建发布目录...
    if not exist "发布版本" mkdir "发布版本"
    
    echo 📁 复制程序文件...
    xcopy "%BUILD_DIR%\*" "发布版本\" /E /I /Y >nul
    
    echo 📄 创建使用说明...
    copy "使用方法.txt" "发布版本\使用方法.txt" >nul
    
    echo 📝 创建启动说明...
    (
        echo 🎯 TIF转PNG转换工具 - 绿色版
        echo.
        echo 📋 使用方法：
        echo 1. 双击 "TIF转PNG转换工具.exe" 启动程序
        echo 2. 选择转换模式（单文件或批量转换）
        echo 3. 选择输入文件或文件夹
        echo 4. 点击"开始转换"按钮
        echo.
        echo 💡 提示：
        echo - 本程序为绿色版，无需安装，可直接运行
        echo - 支持.tif和.tiff格式文件
        echo - 转换后的PNG文件默认保存在原文件目录
        echo.
        echo 📞 如有问题，请查看"使用方法.txt"文件
    ) > "发布版本\README.txt"
    
    echo.
    echo 🎉 打包完成！
    echo.
    echo 📁 程序文件位置: %cd%\发布版本\
    echo 🚀 可执行文件: TIF转PNG转换工具.exe
    echo.
    echo 💡 现在可以将"发布版本"文件夹复制到任何Windows电脑上使用
    echo    无需安装Python或其他依赖，直接双击exe文件即可运行！
    echo.
    
    echo 🔍 是否要打开发布目录？ (Y/N)
    set /p choice=请选择: 
    if /i "%choice%"=="Y" (
        explorer "发布版本"
    )
    
) else (
    echo ❌ 找不到构建输出目录
    echo 💡 请检查打包过程是否有错误
)

echo.
echo 📋 打包过程完成！
echo 按任意键退出...
pause >nul
