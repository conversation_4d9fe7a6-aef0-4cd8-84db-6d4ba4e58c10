# TIF转PNG转换工具 - 快速入门

## 🚀 最简单的使用方法

### 1. 启动程序
双击 `启动GUI.bat` 文件即可启动图形界面

### 2. 三步完成转换
1. **选择模式**：单文件转换 或 批量转换
2. **选择文件**：点击"选择文件/目录"按钮
3. **开始转换**：点击"🚀 开始转换"按钮

### 3. 查看结果
转换完成后会弹出提示，PNG文件默认保存在原文件目录

---

## 📸 界面截图说明

```
┌─────────────────────────────────────────┐
│        TIF to PNG 转换工具              │
├─────────────────────────────────────────┤
│ 转换模式: ○单文件转换 ●批量转换         │
├─────────────────────────────────────────┤
│ 输入路径: [C:\图片\TIF文件夹] [选择目录] │
│ 输出路径: [C:\图片\PNG文件夹] [选择目录] │
├─────────────────────────────────────────┤
│        [🚀 开始转换] [🗑️ 清空]          │
├─────────────────────────────────────────┤
│ 进度: 转换中... (3/10)                  │
│ ████████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ │
├─────────────────────────────────────────┤
│ 转换日志:                               │
│ ✅ 找到 10 个TIF文件，开始转换...       │
│ [1/10] 🔄 处理: image1.tif             │
│     ✅ 转换成功                         │
│ [2/10] 🔄 处理: image2.tif             │
│     ✅ 转换成功                         │
│ ...                                     │
└─────────────────────────────────────────┘
```

---

## 💡 使用技巧

### 单文件转换
- 适合转换少量文件
- 可以精确指定输出文件名和位置
- 支持拖拽文件到输入框

### 批量转换
- 适合转换大量文件
- 自动处理文件夹中所有TIF文件
- 保持原文件名，只改变扩展名

### 输出设置
- **留空输出路径**：PNG文件保存在原TIF文件目录
- **指定输出路径**：所有PNG文件保存到指定目录
- **单文件模式**：可以重命名输出文件

---

## ⚡ 常见问题

**Q: 程序启动不了？**
A: 确保安装了Python和Pillow库：`pip install Pillow`

**Q: 找不到TIF文件？**
A: 检查文件扩展名是否为.tif或.tiff

**Q: 转换很慢？**
A: 大文件转换需要时间，请耐心等待

**Q: 想要命令行版本？**
A: 使用 `python tif.py 输入文件 [输出文件]`

---

## 📋 支持的格式

| 输入格式 | 输出格式 | 说明 |
|---------|---------|------|
| .tif    | .png    | 标准TIF格式 |
| .tiff   | .png    | TIF格式变体 |

---

## 🎯 快速示例

### 示例1：转换单个文件
1. 选择"单文件转换"
2. 点击"选择文件"，选择 `photo.tif`
3. 点击"开始转换"
4. 完成后得到 `photo.png`

### 示例2：批量转换文件夹
1. 选择"批量转换"
2. 点击"选择目录"，选择包含TIF文件的文件夹
3. 点击"开始转换"
4. 所有TIF文件都会转换为PNG格式

---

**🎉 就是这么简单！开始使用吧！**
