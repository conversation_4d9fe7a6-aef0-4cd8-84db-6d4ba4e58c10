===============================================
    TIF转PNG转换工具 - 使用方法说明
===============================================

本工具提供两种使用方式：图形界面版本（推荐）和命令行版本

===============================================
🎨 方式一：图形界面版本（推荐新手使用）
===============================================

【启动方法】
1. 双击 "启动GUI.bat" 文件
   或
2. 在命令行输入：python tif_to_png_gui.py

【操作步骤】
1. 选择转换模式：
   ○ 单文件转换：转换单个TIF文件
   ○ 批量转换：转换整个文件夹中的所有TIF文件

2. 选择输入文件/文件夹：
   - 点击"选择文件"或"选择目录"按钮
   - 浏览并选择要转换的TIF文件或包含TIF文件的文件夹

3. 选择输出位置（可选）：
   - 单文件模式：可指定输出PNG文件的保存位置
   - 批量模式：可指定输出文件夹
   - 💡 如果不选择输出位置，PNG文件将保存在原文件所在目录

4. 开始转换：
   - 点击"🚀 开始转换"按钮
   - 在日志区域查看转换进度和结果
   - 转换完成后会弹出提示框

【界面说明】
- 转换模式：单选按钮，选择单文件或批量转换
- 输入路径：显示选择的文件或文件夹路径
- 输出路径：显示输出位置（可选）
- 进度条：显示转换进度
- 转换日志：显示详细的转换过程和结果
- 🗑️ 清空按钮：清除所有输入内容

===============================================
⌨️ 方式二：命令行版本（适合高级用户）
===============================================

【基本语法】
python tif.py [输入路径] [输出路径]

【使用方法】
1. 转换单个文件：
   python tif.py 文件名.tif

2. 转换单个文件并指定输出：
   python tif.py 文件名.tif 输出名.png

3. 批量转换文件夹：
   python tif.py 输入文件夹

4. 批量转换到指定文件夹：
   python tif.py 输入文件夹 输出文件夹

【命令行示例】
python tif.py test.tif
python tif.py test.tif output.png
python tif.py images
python tif.py images converted
python tif.py "C:\我的图片\TIF文件" "C:\转换结果"

===============================================
📋 功能特点
===============================================

✅ 支持的输入格式：.tif, .tiff
✅ 输出格式：.png
✅ 智能颜色模式处理：
   - 自动保持RGBA/LA模式的透明度
   - 调色板模式转换为RGBA
   - 其他模式转换为RGB
✅ 批量处理：一次转换整个文件夹
✅ 错误处理：详细的错误提示和异常处理
✅ 进度显示：实时显示转换进度
✅ 日志记录：详细的操作记录

===============================================
⚠️ 注意事项
===============================================

1. 确保已安装Python和PIL库：
   pip install Pillow

2. 输入文件必须是有效的TIF/TIFF格式

3. 确保有足够的磁盘空间存储转换后的PNG文件

4. 批量转换时，只会处理.tif和.tiff扩展名的文件

5. 如果输出文件已存在，将会被覆盖

6. 转换过程中请勿关闭程序窗口

===============================================
🔧 故障排除
===============================================

【问题】程序无法启动
【解决】检查是否安装了Python和Pillow库

【问题】提示"文件不存在"
【解决】检查输入路径是否正确，文件是否存在

【问题】转换失败
【解决】检查TIF文件是否损坏，是否有读取权限

【问题】GUI界面显示异常
【解决】尝试重新启动程序，检查屏幕分辨率设置

===============================================
📞 技术支持
===============================================

如遇到问题，请检查：
1. Python版本（建议3.7+）
2. Pillow库版本（建议8.0+）
3. 文件权限和路径
4. 磁盘空间

版本：v1.0
更新日期：2024-07-04
