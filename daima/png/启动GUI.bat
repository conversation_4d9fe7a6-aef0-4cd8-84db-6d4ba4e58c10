@echo off
chcp 65001 >nul
title TIF转PNG转换工具

echo ========================================
echo    TIF to PNG Converter GUI
echo    TIF转PNG转换工具图形界面版本
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 检查运行环境...
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python: https://www.python.org/downloads/
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重启电脑后再试
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境正常
python --version

REM 检查PIL库
echo 🔍 检查图像处理库...
python -c "import PIL; print('✅ PIL库可用')" 2>nul
if errorlevel 1 (
    echo ❌ PIL库未安装，正在自动安装...
    pip install Pillow
    if errorlevel 1 (
        echo ❌ 自动安装失败，请手动安装: pip install Pillow
        pause
        exit /b 1
    )
)

REM 检查tkinter
echo 🔍 检查GUI库...
python -c "import tkinter; print('✅ GUI库可用')" 2>nul
if errorlevel 1 (
    echo ❌ GUI库不可用，请重新安装Python
    pause
    exit /b 1
)

REM 检查主程序文件
if not exist "tif_to_png_gui.py" (
    echo ❌ 找不到主程序文件
    echo 💡 请确保在正确的目录运行此脚本
    pause
    exit /b 1
)

echo.
echo ✅ 环境检查完成，正在启动GUI界面...
echo.

REM 启动GUI程序
python tif_to_png_gui.py

REM 检查退出状态
if errorlevel 1 (
    echo.
    echo ❌ 程序运行时发生错误
    echo 💡 可能的原因：
    echo 1. 文件权限问题
    echo 2. 缺少依赖库
    echo 3. 系统兼容性问题
    echo.
) else (
    echo.
    echo ✅ 程序正常退出
)

echo.
echo 按任意键关闭窗口...
pause >nul
