@echo off
chcp 65001 >nul
title 打包TIF转PNG转换工具

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 TIF转PNG转换工具打包程序                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🚀 开始打包程序，请稍候...
echo.

REM 清理旧文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"

echo 📦 正在使用PyInstaller打包...
echo.

REM 使用PyInstaller打包
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="TIF转PNG转换工具" ^
    --add-data="tif.py;." ^
    --distpath="绿色版程序" ^
    --workpath="temp_build" ^
    tif_to_png_gui.py

if errorlevel 1 (
    echo ❌ 打包失败！
    echo.
    echo 💡 可能的原因：
    echo 1. PyInstaller未正确安装
    echo 2. 缺少必要的依赖库
    echo 3. 磁盘空间不足
    echo.
    pause
    exit /b 1
)

echo ✅ 打包成功！
echo.

REM 复制相关文件到发布目录
echo 📁 整理发布文件...

copy "使用方法.txt" "绿色版程序\" >nul
copy "给普通用户的说明.txt" "绿色版程序\使用说明.txt" >nul

REM 复制测试图片
if exist "test_images" (
    xcopy "test_images" "绿色版程序\test_images\" /E /I /Y >nul
)

REM 创建使用说明
(
echo 🎯 TIF转PNG转换工具 - 绿色免安装版
echo.
echo 📋 使用方法：
echo 1. 双击 "TIF转PNG转换工具.exe" 启动程序
echo 2. 选择转换模式（单文件或批量转换）
echo 3. 选择要转换的TIF文件或文件夹
echo 4. 点击"开始转换"按钮
echo.
echo 💡 特点：
echo ✅ 绿色软件，无需安装Python
echo ✅ 支持.tif和.tiff格式文件
echo ✅ 支持单文件和批量转换
echo ✅ 自动保持图片质量和透明度
echo.
echo 🧪 测试：
echo 可以使用"test_images"文件夹中的测试图片验证功能
echo.
echo 📞 详细说明请查看"使用方法.txt"文件
) > "绿色版程序\README.txt"

echo.
echo 🎉 打包完成！
echo.
echo 📁 程序位置: %cd%\绿色版程序\
echo 🚀 主程序: TIF转PNG转换工具.exe
echo 📄 说明文件: README.txt, 使用说明.txt, 使用方法.txt
echo 🧪 测试图片: test_images文件夹
echo.

REM 显示文件大小
for %%f in ("绿色版程序\TIF转PNG转换工具.exe") do (
    set size=%%~zf
    set /a size_mb=!size!/1024/1024
    echo 📊 程序大小: !size_mb! MB
)

echo.
echo 💡 现在可以将"绿色版程序"文件夹复制到任何Windows电脑使用！
echo    无需安装Python或任何依赖，直接双击exe文件即可运行！
echo.

echo 🔍 是否要打开程序目录？ (Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    explorer "绿色版程序"
)

echo.
echo 🧪 是否要测试运行程序？ (Y/N)
set /p test_choice=请选择: 
if /i "%test_choice%"=="Y" (
    echo 🚀 启动测试...
    start "" "绿色版程序\TIF转PNG转换工具.exe"
)

REM 清理临时文件
if exist "temp_build" rmdir /s /q "temp_build"

echo.
echo 📋 打包完成！按任意键退出...
pause >nul
