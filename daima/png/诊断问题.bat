@echo off
chcp 65001 >nul
title 诊断TIF转PNG工具问题

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    问题诊断工具                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 正在诊断问题...
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤1: 检查Python环境
echo ═══════════════════════════════════════════════════════════════

echo 检查Python是否安装...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 💡 解决方案：
    echo 1. 下载Python: https://www.python.org/downloads/
    echo 2. 安装时务必勾选 "Add Python to PATH"
    echo 3. 重启电脑
    echo.
    goto :end
) else (
    echo ✅ Python环境正常
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤2: 检查必要库
echo ═══════════════════════════════════════════════════════════════

echo 检查PIL库...
python -c "import PIL; print('✅ PIL库版本:', PIL.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PIL库未安装
    echo 💡 正在尝试安装...
    pip install Pillow
    if errorlevel 1 (
        echo ❌ 安装失败，请手动运行: pip install Pillow
        goto :end
    )
    echo ✅ PIL库安装成功
) else (
    echo ✅ PIL库正常
)

echo 检查tkinter库...
python -c "import tkinter; print('✅ tkinter库可用')" 2>nul
if errorlevel 1 (
    echo ❌ tkinter库不可用
    echo 💡 请重新安装Python，确保包含tkinter
    goto :end
) else (
    echo ✅ tkinter库正常
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤3: 检查程序文件
echo ═══════════════════════════════════════════════════════════════

if exist "tif_to_png_gui.py" (
    echo ✅ 主程序文件存在
) else (
    echo ❌ 找不到主程序文件: tif_to_png_gui.py
    goto :end
)

if exist "tif.py" (
    echo ✅ 转换模块存在
) else (
    echo ❌ 找不到转换模块: tif.py
    goto :end
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤4: 测试程序启动
echo ═══════════════════════════════════════════════════════════════

echo 🚀 尝试启动GUI程序...
echo 如果程序正常，应该会出现图形界面窗口
echo.

python tif_to_png_gui.py

echo.
if errorlevel 1 (
    echo ❌ 程序启动失败
    echo.
    echo 💡 可能的原因：
    echo 1. 代码语法错误
    echo 2. 缺少依赖库
    echo 3. 系统兼容性问题
    echo 4. 文件权限问题
    echo.
) else (
    echo ✅ 程序启动成功（如果看到了GUI界面）
    echo.
)

:end
echo ═══════════════════════════════════════════════════════════════
echo 📋 诊断完成
echo ═══════════════════════════════════════════════════════════════
echo.
echo 💡 如果仍有问题，请：
echo 1. 截图错误信息
echo 2. 检查是否有杀毒软件拦截
echo 3. 尝试以管理员身份运行
echo 4. 重启电脑后再试
echo.
echo 按任意键退出...
pause >nul
