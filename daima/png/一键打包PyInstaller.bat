@echo off
chcp 65001 >nul
title TIF转PNG工具 - PyInstaller一键打包

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG转换工具                          ║
echo ║                 PyInstaller一键打包脚本                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔧 开始使用PyInstaller打包程序...
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤1: 检查环境
echo ═══════════════════════════════════════════════════════════════
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo 🔍 检查PyInstaller...
python -c "import PyInstaller; print('✅ PyInstaller已安装')" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo 🔍 检查PIL库...
python -c "import PIL; print('✅ PIL版本:', PIL.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PIL库未安装，正在安装...
    pip install Pillow
    if errorlevel 1 (
        echo ❌ PIL库安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤2: 清理旧文件
echo ═══════════════════════════════════════════════════════════════
if exist "build" (
    echo 🗑️ 删除build目录...
    rmdir /s /q "build"
)
if exist "dist" (
    echo 🗑️ 删除dist目录...
    rmdir /s /q "dist"
)
if exist "*.spec" (
    echo 🗑️ 删除spec文件...
    del *.spec
)
echo ✅ 清理完成
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤3: 创建测试图片（如果需要）
echo ═══════════════════════════════════════════════════════════════
if not exist "test_images" (
    echo 🎨 创建测试图片...
    python create_test_images.py
) else (
    echo ✅ 测试图片已存在
)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤4: 开始打包
echo ═══════════════════════════════════════════════════════════════
echo 🚀 正在使用PyInstaller打包，请稍候...
echo.

REM 使用PyInstaller打包，参数说明：
REM --onefile: 打包成单个exe文件
REM --windowed: 不显示控制台窗口
REM --name: 指定exe文件名
REM --add-data: 添加数据文件
REM --hidden-import: 添加隐藏导入
REM --icon: 指定图标文件（如果存在）

set ICON_PARAM=
if exist "icon.ico" set ICON_PARAM=--icon=icon.ico

pyinstaller --onefile --windowed ^
    --name="TIF转PNG转换工具" ^
    --add-data="tif.py;." ^
    --add-data="使用方法.txt;." ^
    --add-data="test_images;test_images" ^
    --hidden-import=PIL ^
    --hidden-import=PIL.Image ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=threading ^
    --hidden-import=pathlib ^
    %ICON_PARAM% ^
    tif_to_png_gui.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 💡 可能的原因：
    echo    1. 缺少必要的依赖库
    echo    2. 文件路径包含特殊字符
    echo    3. 磁盘空间不足
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 打包成功！
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤5: 整理发布文件
echo ═══════════════════════════════════════════════════════════════

if exist "dist\TIF转PNG转换工具.exe" (
    echo 📦 创建发布目录...
    if not exist "绿色版发布" mkdir "绿色版发布"
    
    echo 📁 复制主程序...
    copy "dist\TIF转PNG转换工具.exe" "绿色版发布\" >nul
    
    echo 📄 复制说明文件...
    copy "使用方法.txt" "绿色版发布\使用方法.txt" >nul
    copy "给普通用户的说明.txt" "绿色版发布\README.txt" >nul
    
    echo 📸 复制测试图片...
    if exist "test_images" (
        xcopy "test_images" "绿色版发布\test_images\" /E /I /Y >nul
    )
    
    echo 📝 创建快速启动说明...
    (
        echo 🎯 TIF转PNG转换工具 - 绿色免安装版
        echo.
        echo 📋 快速使用：
        echo 1. 双击 "TIF转PNG转换工具.exe" 启动程序
        echo 2. 选择转换模式（单文件或批量转换）
        echo 3. 选择要转换的TIF文件或文件夹
        echo 4. 点击"开始转换"按钮即可
        echo.
        echo 💡 特点：
        echo ✅ 绿色软件，无需安装，直接运行
        echo ✅ 支持.tif和.tiff格式文件
        echo ✅ 支持单文件和批量转换
        echo ✅ 自动保持图片质量和透明度
        echo ✅ 转换后的PNG文件默认保存在原目录
        echo.
        echo 📞 详细说明请查看"使用方法.txt"和"README.txt"
        echo.
        echo 🧪 测试：
        echo 可以使用"test_images"文件夹中的测试图片验证功能
    ) > "绿色版发布\快速使用说明.txt"
    
    echo.
    echo 🎉 打包完成！
    echo.
    echo 📁 发布文件位置: %cd%\绿色版发布\
    echo 🚀 主程序文件: TIF转PNG转换工具.exe
    echo 📄 说明文件: 快速使用说明.txt, README.txt, 使用方法.txt
    echo 🧪 测试图片: test_images文件夹
    echo.
    echo 💡 现在可以将"绿色版发布"文件夹复制到任何Windows电脑上使用！
    echo    无需安装Python或任何依赖，直接双击exe文件即可运行！
    echo.
    
    REM 显示文件大小信息
    for %%f in ("绿色版发布\TIF转PNG转换工具.exe") do (
        set size=%%~zf
        set /a size_mb=!size!/1024/1024
        echo 📊 程序大小: !size_mb! MB
    )
    
    echo.
    echo 🔍 是否要打开发布目录？ (Y/N)
    set /p choice=请选择: 
    if /i "%choice%"=="Y" (
        explorer "绿色版发布"
    )
    
    echo.
    echo 🧪 是否要测试运行程序？ (Y/N)
    set /p test_choice=请选择: 
    if /i "%test_choice%"=="Y" (
        echo 🚀 启动测试...
        start "" "绿色版发布\TIF转PNG转换工具.exe"
    )
    
) else (
    echo ❌ 找不到生成的exe文件
    echo 💡 请检查打包过程是否有错误
)

echo.
echo 📋 打包过程完成！
echo 按任意键退出...
pause >nul
