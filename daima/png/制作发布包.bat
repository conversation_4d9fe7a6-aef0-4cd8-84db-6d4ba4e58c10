@echo off
chcp 65001 >nul
title 制作TIF转PNG工具发布包

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                制作TIF转PNG工具发布包                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 📦 正在制作发布包...
echo.

REM 创建发布目录
set RELEASE_DIR=TIF转PNG转换工具_绿色版
if exist "%RELEASE_DIR%" rmdir /s /q "%RELEASE_DIR%"
mkdir "%RELEASE_DIR%"

echo 📁 复制核心程序文件...
copy "tif_to_png_gui.py" "%RELEASE_DIR%\" >nul
copy "tif.py" "%RELEASE_DIR%\" >nul
copy "create_test_images.py" "%RELEASE_DIR%\" >nul

echo 📁 复制启动器...
copy "绿色版启动器.bat" "%RELEASE_DIR%\启动程序.bat" >nul

echo 📁 复制说明文档...
copy "使用方法.txt" "%RELEASE_DIR%\" >nul
copy "给普通用户的说明.txt" "%RELEASE_DIR%\用户指南.txt" >nul

echo 📁 复制测试图片...
if exist "test_images" (
    xcopy "test_images" "%RELEASE_DIR%\test_images\" /E /I /Y >nul
)

echo 📝 创建快速使用说明...
(
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG转换工具                          ║
echo ║                      绿色免安装版                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎯 这是一个简单易用的图片格式转换工具
echo    可以将TIF格式的图片转换为PNG格式
echo.
echo 📋 使用方法：
echo 1. 双击 "启动程序.bat" 启动工具
echo 2. 首次运行会自动检查和安装必要组件
echo 3. 选择转换模式（单文件或批量转换）
echo 4. 选择要转换的TIF文件或文件夹
echo 5. 点击"开始转换"按钮
echo.
echo 💡 系统要求：
echo ✅ Windows 7/8/10/11
echo ✅ 需要安装Python 3.7或更高版本
echo    （如果没有，程序会提供安装指导）
echo.
echo 🧪 测试功能：
echo - 可以使用"test_images"文件夹中的测试图片
echo - 验证程序功能是否正常
echo.
echo 📞 详细说明：
echo - 查看"用户指南.txt"获取详细使用说明
echo - 查看"使用方法.txt"获取技术细节
echo.
echo 🎉 开始使用：
echo 双击"启动程序.bat"即可开始！
) > "%RELEASE_DIR%\README.txt"

echo 📝 创建Python安装指导...
(
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Python安装指导                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 如果双击"启动程序.bat"提示缺少Python，请按以下步骤安装：
echo.
echo 📋 安装步骤：
echo 1. 访问Python官网：https://www.python.org/downloads/
echo 2. 点击"Download Python"下载最新版本
echo 3. 运行下载的安装程序
echo 4. ⚠️ 重要：勾选"Add Python to PATH"选项
echo 5. 点击"Install Now"开始安装
echo 6. 安装完成后重启电脑
echo 7. 重新运行"启动程序.bat"
echo.
echo 💡 安装提示：
echo - 选择"Add Python to PATH"非常重要
echo - 如果忘记勾选，需要重新安装Python
echo - 安装过程需要管理员权限
echo.
echo 🔧 验证安装：
echo 安装完成后，可以按Win+R，输入cmd，然后输入：
echo python --version
echo 如果显示版本号，说明安装成功
echo.
echo ❓ 常见问题：
echo Q: 提示"python不是内部或外部命令"？
echo A: 说明没有勾选"Add Python to PATH"，请重新安装
echo.
echo Q: 安装失败？
echo A: 确保以管理员身份运行安装程序
) > "%RELEASE_DIR%\Python安装指导.txt"

echo 📝 创建故障排除指南...
(
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    故障排除指南                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔧 常见问题及解决方案：
echo.
echo 【问题1】双击"启动程序.bat"没反应
echo 💡 解决方案：
echo - 右键选择"以管理员身份运行"
echo - 检查是否被杀毒软件拦截
echo - 确保文件路径不包含中文或特殊字符
echo.
echo 【问题2】提示"找不到Python"
echo 💡 解决方案：
echo - 按照"Python安装指导.txt"安装Python
echo - 确保安装时勾选了"Add Python to PATH"
echo - 安装后重启电脑
echo.
echo 【问题3】提示"PIL库未安装"
echo 💡 解决方案：
echo - 程序会自动尝试安装
echo - 如果自动安装失败，手动运行：pip install Pillow
echo.
echo 【问题4】转换失败
echo 💡 解决方案：
echo - 确保输入文件是有效的TIF/TIFF格式
echo - 检查磁盘空间是否充足
echo - 确保对文件夹有读写权限
echo.
echo 【问题5】程序运行缓慢
echo 💡 解决方案：
echo - 大文件转换需要时间，请耐心等待
echo - 关闭其他占用内存的程序
echo - 一次处理的文件数量不要太多
echo.
echo 【问题6】转换后图片质量差
echo 💡 解决方案：
echo - 程序使用最佳质量设置
echo - 如果原图质量就不高，转换后也会如此
echo - PNG格式可能比TIF文件大，这是正常的
echo.
echo 📞 获取帮助：
echo 如果以上方案都无法解决问题，请：
echo 1. 截图错误信息
echo 2. 记录操作步骤
echo 3. 联系技术支持
) > "%RELEASE_DIR%\故障排除指南.txt"

echo.
echo ✅ 发布包制作完成！
echo.
echo 📁 发布包位置: %cd%\%RELEASE_DIR%\
echo 📋 包含文件:
echo   - 启动程序.bat （主启动器）
echo   - tif_to_png_gui.py （主程序）
echo   - tif.py （转换模块）
echo   - test_images\ （测试图片）
echo   - README.txt （快速使用说明）
echo   - 用户指南.txt （详细说明）
echo   - Python安装指导.txt （安装指导）
echo   - 故障排除指南.txt （问题解决）
echo.
echo 💡 使用方法：
echo 1. 将整个"%RELEASE_DIR%"文件夹复制到目标电脑
echo 2. 双击"启动程序.bat"即可使用
echo 3. 首次使用会自动检查和配置环境
echo.
echo 🔍 是否要打开发布包目录？ (Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    explorer "%RELEASE_DIR%"
)

echo.
echo 🧪 是否要测试发布包？ (Y/N)
set /p test_choice=请选择: 
if /i "%test_choice%"=="Y" (
    echo 🚀 启动测试...
    start "" "%RELEASE_DIR%\启动程序.bat"
)

echo.
echo 📋 发布包制作完成！
echo 现在可以将"%RELEASE_DIR%"文件夹分享给其他用户了！
echo.
echo 按任意键退出...
pause >nul
